import 'package:cloud_firestore/cloud_firestore.dart';

class Product {
  final String id;
  final String title;
  final String description;
  final double price;
  final String category;
  final String condition;
  final String? brand;
  final int stock;
  final List<String> images;
  final String sellerId;
  final String sellerName;
  final String? sellerAvatar;
  final String location;
  final bool isNegotiable;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final double? rating;
  final int viewCount;
  final int favoriteCount;
  final Map<String, dynamic>? metadata;

  const Product({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.category,
    required this.condition,
    this.brand,
    required this.stock,
    required this.images,
    required this.sellerId,
    required this.sellerName,
    this.sellerAvatar,
    required this.location,
    this.isNegotiable = false,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.rating,
    this.viewCount = 0,
    this.favoriteCount = 0,
    this.metadata,
  });

  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Product(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      category: data['category'] ?? '',
      condition: data['condition'] ?? 'New',
      brand: data['brand'],
      stock: data['stock'] ?? 0,
      images: List<String>.from(data['images'] ?? []),
      sellerId: data['sellerId'] ?? '',
      sellerName: data['sellerName'] ?? '',
      sellerAvatar: data['sellerAvatar'],
      location: data['location'] ?? '',
      isNegotiable: data['isNegotiable'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      rating: data['rating']?.toDouble(),
      viewCount: data['viewCount'] ?? 0,
      favoriteCount: data['favoriteCount'] ?? 0,
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'price': price,
      'category': category,
      'condition': condition,
      'brand': brand,
      'stock': stock,
      'images': images,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerAvatar': sellerAvatar,
      'location': location,
      'isNegotiable': isNegotiable,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
      'rating': rating,
      'viewCount': viewCount,
      'favoriteCount': favoriteCount,
      'metadata': metadata,
    };
  }

  Product copyWith({
    String? id,
    String? title,
    String? description,
    double? price,
    String? category,
    String? condition,
    String? brand,
    int? stock,
    List<String>? images,
    String? sellerId,
    String? sellerName,
    String? sellerAvatar,
    String? location,
    bool? isNegotiable,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    double? rating,
    int? viewCount,
    int? favoriteCount,
    Map<String, dynamic>? metadata,
  }) {
    return Product(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      condition: condition ?? this.condition,
      brand: brand ?? this.brand,
      stock: stock ?? this.stock,
      images: images ?? this.images,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      sellerAvatar: sellerAvatar ?? this.sellerAvatar,
      location: location ?? this.location,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      rating: rating ?? this.rating,
      viewCount: viewCount ?? this.viewCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Product(id: $id, title: $title, price: $price, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods for custom fields and metadata

  /// Get a custom field value from metadata
  T? getCustomField<T>(String key) {
    if (metadata == null) return null;
    final value = metadata![key];
    if (value is T) return value;
    return null;
  }

  /// Set a custom field in metadata
  Product setCustomField(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata[key] = value;
    return copyWith(metadata: newMetadata);
  }

  /// Get technical specifications for electronics
  Map<String, dynamic> get techSpecs {
    if (metadata == null) return {};
    return Map<String, dynamic>.from(metadata!['techSpecs'] ?? {});
  }

  /// Set technical specifications
  Product setTechSpecs(Map<String, dynamic> specs) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata['techSpecs'] = specs;
    return copyWith(metadata: newMetadata);
  }

  /// Get custom tags
  List<String> get customTags {
    if (metadata == null) return [];
    return List<String>.from(metadata!['customTags'] ?? []);
  }

  /// Set custom tags
  Product setCustomTags(List<String> tags) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata['customTags'] = tags;
    return copyWith(metadata: newMetadata);
  }

  // Common tech specification getters
  String? get processor => getCustomField<String>('processor');
  String? get ram => getCustomField<String>('ram');
  String? get storage => getCustomField<String>('storage');
  String? get screenSize => getCustomField<String>('screenSize');
  String? get operatingSystem => getCustomField<String>('operatingSystem');
  String? get connectivity => getCustomField<String>('connectivity');
  String? get batteryLife => getCustomField<String>('batteryLife');
  String? get warranty => getCustomField<String>('warranty');
  String? get color => getCustomField<String>('color');
  String? get material => getCustomField<String>('material');
  String? get dimensions => getCustomField<String>('dimensions');
  String? get weight => getCustomField<String>('weight');

  /// Get category-specific fields based on product category
  Map<String, dynamic> getCategoryFields() {
    switch (category.toLowerCase()) {
      case 'electronics':
        return {
          'processor': processor,
          'ram': ram,
          'storage': storage,
          'screenSize': screenSize,
          'operatingSystem': operatingSystem,
          'connectivity': connectivity,
          'batteryLife': batteryLife,
          'warranty': warranty,
        }..removeWhere((key, value) => value == null);

      case 'clothing & fashion':
        return {
          'color': color,
          'material': material,
          'size': getCustomField<String>('size'),
          'fit': getCustomField<String>('fit'),
          'care': getCustomField<String>('care'),
        }..removeWhere((key, value) => value == null);

      case 'furniture':
        return {
          'material': material,
          'dimensions': dimensions,
          'weight': weight,
          'color': color,
          'assembly': getCustomField<String>('assembly'),
          'warranty': warranty,
        }..removeWhere((key, value) => value == null);

      case 'books & stationery':
        return {
          'author': getCustomField<String>('author'),
          'publisher': getCustomField<String>('publisher'),
          'isbn': getCustomField<String>('isbn'),
          'language': getCustomField<String>('language'),
          'pages': getCustomField<String>('pages'),
          'edition': getCustomField<String>('edition'),
        }..removeWhere((key, value) => value == null);

      default:
        return techSpecs;
    }
  }

  /// Get all displayable custom fields
  Map<String, dynamic> getAllCustomFields() {
    final categoryFields = getCategoryFields();
    final otherFields = Map<String, dynamic>.from(metadata ?? {});

    // Remove already categorized fields
    otherFields.remove('techSpecs');
    otherFields.remove('customTags');

    return {...categoryFields, ...otherFields};
  }
}
